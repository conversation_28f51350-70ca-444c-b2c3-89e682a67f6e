"""
Advanced RAG MCQ Generator - The Grounded Scholar

This module implements the Grounded Scholar approach using RAG to create
highly specific, factually accurate MCQs grounded in user's source material.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .mcq_validator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationResult, fix_common_mcq_issues

logger = logging.getLogger(__name__)


class CognitiveLevel(Enum):
    """Bloom's Taxonomy cognitive levels"""
    REMEMBERING = "remembering"
    UNDERSTANDING = "understanding"
    APPLYING = "applying"
    ANALYZING = "analyzing"
    EVALUATING = "evaluating"
    CREATING = "creating"


@dataclass
class MCQGenerationContext:
    """Context for MCQ generation"""
    topic: str
    difficulty: str
    cognitive_level: CognitiveLevel
    context_passages: List[str]
    source_documents: List[str]
    target_audience: str


class AdvancedRAGMCQGenerator:
    """
    The Grounded Scholar - Advanced RAG-based MCQ generator
    
    This generator uses RAG to retrieve specific, relevant passages from
    the user's uploaded books and creates questions grounded in that content.
    """
    
    def __init__(self, rag_engine=None, model_interface=None):
        self.rag_engine = rag_engine
        self.model_interface = model_interface
        self.validator = MCQValidator()
        
        # Cognitive level prompts
        self.cognitive_prompts = {
            CognitiveLevel.REMEMBERING: "Create a question that tests recall of facts and basic concepts from the context.",
            CognitiveLevel.UNDERSTANDING: "Create a question that tests explanation and comprehension of ideas from the context.",
            CognitiveLevel.APPLYING: "Create a question that tests application of information to new situations based on the context.",
            CognitiveLevel.ANALYZING: "Create a question that tests analysis of relationships and connections in the context.",
            CognitiveLevel.EVALUATING: "Create a question that tests evaluation and judgment based on the context.",
            CognitiveLevel.CREATING: "Create a question that tests synthesis and creation of new ideas from the context."
        }
    
    async def generate_grounded_mcq(self, topic: str, difficulty: str = "medium", 
                                   cognitive_level: CognitiveLevel = CognitiveLevel.UNDERSTANDING,
                                   max_retries: int = 3) -> Dict[str, Any]:
        """
        Generate a high-quality MCQ using the Grounded Scholar approach
        
        Args:
            topic: The topic for the question
            difficulty: Question difficulty level
            cognitive_level: Bloom's taxonomy level
            max_retries: Maximum number of generation attempts
            
        Returns:
            Dictionary containing the generated MCQ
        """
        try:
            logger.info(f"🔍 Generating grounded MCQ for topic: {topic}")
            
            # Step 1: Retrieve relevant context using RAG
            context_passages = await self._retrieve_context(topic)
            
            if not context_passages:
                logger.warning("No relevant context found, falling back to basic generation")
                return await self._generate_fallback_mcq(topic, difficulty)
            
            # Step 2: Create generation context
            generation_context = MCQGenerationContext(
                topic=topic,
                difficulty=difficulty,
                cognitive_level=cognitive_level,
                context_passages=context_passages,
                source_documents=[],  # Will be populated by RAG engine
                target_audience=self._get_target_audience(difficulty)
            )
            
            # Step 3: Generate MCQ with validation loop
            for attempt in range(max_retries):
                try:
                    mcq = await self._generate_mcq_with_context(generation_context)
                    
                    # Step 4: Validate the generated MCQ
                    validation_result = self.validator.validate_mcq(mcq)
                    
                    if validation_result.is_valid:
                        logger.info(f"✅ Generated valid grounded MCQ (attempt {attempt + 1})")
                        mcq['validation_score'] = validation_result.score
                        mcq['grounded'] = True
                        mcq['source_passages'] = len(context_passages)
                        return mcq
                    else:
                        logger.warning(f"Generated MCQ failed validation (attempt {attempt + 1}): {validation_result.issues}")
                        
                        # Try to fix common issues
                        if attempt < max_retries - 1:
                            mcq = fix_common_mcq_issues(mcq)
                            validation_result = self.validator.validate_mcq(mcq)
                            if validation_result.is_valid:
                                logger.info(f"✅ Fixed MCQ validation issues (attempt {attempt + 1})")
                                mcq['validation_score'] = validation_result.score
                                mcq['grounded'] = True
                                mcq['source_passages'] = len(context_passages)
                                return mcq
                
                except Exception as e:
                    logger.error(f"Error in MCQ generation attempt {attempt + 1}: {e}")
                    if attempt == max_retries - 1:
                        raise
            
            # If all attempts failed, return fallback
            logger.warning("All MCQ generation attempts failed, using fallback")
            return await self._generate_fallback_mcq(topic, difficulty)
            
        except Exception as e:
            logger.error(f"❌ Error in grounded MCQ generation: {e}")
            return await self._generate_fallback_mcq(topic, difficulty)
    
    async def _retrieve_context(self, topic: str, top_k: int = 3) -> List[str]:
        """Retrieve relevant context passages using RAG"""
        try:
            if not self.rag_engine:
                logger.warning("RAG engine not available")
                return []
            
            # Use RAG engine to retrieve context
            if hasattr(self.rag_engine, 'retrieve_context'):
                contexts = await self.rag_engine.retrieve_context(topic, top_k=top_k)
                logger.info(f"Retrieved {len(contexts)} context passages for topic: {topic}")
                return contexts
            else:
                logger.warning("RAG engine doesn't support retrieve_context method")
                return []
                
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return []
    
    async def _generate_mcq_with_context(self, context: MCQGenerationContext) -> Dict[str, Any]:
        """Generate MCQ using the provided context"""
        
        # Combine context passages
        combined_context = "\n\n".join(context.context_passages)
        
        # Create the Inquisitor's Mandate prompt
        prompt = self._create_inquisitor_prompt(context, combined_context)
        
        # Generate using model interface
        if self.model_interface:
            response = await self.model_interface.generate_text(prompt)
        else:
            # Fallback to basic generation
            return await self._generate_fallback_mcq(context.topic, context.difficulty)
        
        # Parse JSON response
        try:
            # Clean the response (remove markdown formatting if present)
            cleaned_response = self._clean_json_response(response)
            mcq_data = json.loads(cleaned_response)
            
            # Add metadata
            mcq_data['generation_method'] = 'grounded_scholar'
            mcq_data['cognitive_level'] = context.cognitive_level.value
            mcq_data['context_length'] = len(combined_context)
            
            return mcq_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response was: {response}")
            raise ValueError(f"Invalid JSON response from model: {e}")
    
    def _create_inquisitor_prompt(self, context: MCQGenerationContext, combined_context: str) -> str:
        """Create the Inquisitor's Mandate prompt for grounded generation"""
        
        cognitive_instruction = self.cognitive_prompts.get(
            context.cognitive_level, 
            self.cognitive_prompts[CognitiveLevel.UNDERSTANDING]
        )
        
        prompt = f"""### ROLE & GOAL ###
You are a meticulous and demanding university professor designing a final exam. Your goal is to create ONE challenging multiple-choice question that tests a deep understanding of a key concept from the provided context about '{context.topic}'. Your question must not be a simple definition recall. It must require reasoning.

### COGNITIVE LEVEL ###
{cognitive_instruction}

### CONTEXT ###
{combined_context}

### TASK & CONSTRAINTS ###
1. **Identify a Core Concept:** From the CONTEXT, identify a single, non-obvious concept, relationship, or implication related to '{context.topic}'.
2. **Formulate the Question:** Create a clear, specific question about this core concept. The question must be answerable *only* from the provided CONTEXT.
3. **Create the Correct Answer:** Write a correct answer that is a thoughtful paraphrase of information in the context, not a direct quote.
4. **Create Plausible Distractors:** Generate three incorrect but highly plausible "distractor" options. These distractors should target common misconceptions related to the topic. They must be factually incorrect *according to the context*.
5. **Write the Explanation:** Provide a concise explanation for why the correct answer is correct and briefly explain why the other options are incorrect, citing the context.

### TARGET AUDIENCE ###
This question is designed for {context.target_audience}.

### STRICT OUTPUT FORMAT ###
You MUST respond with ONLY a single, raw JSON object. Do not include any introductory text, markdown formatting like ```json, or any other text outside of the JSON structure.

{{
  "question": "Your question text here.",
  "options": {{
    "A": "Plausible distractor A.",
    "B": "The correct answer, paraphrased from the context.",
    "C": "Plausible distractor C.",
    "D": "Plausible distractor D."
  }},
  "correct": "B",
  "explanation": "Your detailed explanation here, citing specific information from the context."
}}"""
        
        return prompt
    
    def _clean_json_response(self, response: str) -> str:
        """Clean the model response to extract valid JSON"""
        # Remove markdown code blocks
        response = response.strip()
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        
        # Find JSON object boundaries
        start = response.find('{')
        end = response.rfind('}') + 1
        
        if start != -1 and end > start:
            return response[start:end]
        
        return response
    
    def _get_target_audience(self, difficulty: str) -> str:
        """Get target audience description based on difficulty"""
        audience_map = {
            "easy": "a high-school student",
            "medium": "an undergraduate university student",
            "hard": "a graduate student specializing in the field",
            "expert": "a post-doctoral researcher"
        }
        return audience_map.get(difficulty.lower(), "an undergraduate university student")
    
    async def _generate_fallback_mcq(self, topic: str, difficulty: str) -> Dict[str, Any]:
        """Generate a fallback MCQ when grounded generation fails"""
        return {
            "question": f"What is a key concept related to {topic}?",
            "options": {
                "A": f"A fundamental principle of {topic}",
                "B": f"An advanced application of {topic}",
                "C": f"A historical development in {topic}",
                "D": f"A theoretical framework for {topic}"
            },
            "correct": "A",
            "explanation": f"This question tests basic understanding of {topic} concepts.",
            "generation_method": "fallback",
            "grounded": False
        }
    
    async def generate_with_two_step_distractors(self, topic: str, difficulty: str = "medium",
                                               cognitive_level: CognitiveLevel = CognitiveLevel.UNDERSTANDING) -> Dict[str, Any]:
        """
        Generate MCQ using two-step process for better distractors

        Step 1: Generate question and correct answer
        Step 2: Generate high-quality distractors
        """
        try:
            logger.info(f"🎯 Generating MCQ with two-step distractor process for: {topic}")

            # Step 1: Retrieve context
            context_passages = await self._retrieve_context(topic)
            if not context_passages:
                return await self._generate_fallback_mcq(topic, difficulty)

            combined_context = "\n\n".join(context_passages)

            # Step 2: Generate question and correct answer only
            question_prompt = self._create_question_only_prompt(topic, combined_context, difficulty, cognitive_level)
            question_response = await self.model_interface.generate_text(question_prompt)

            try:
                question_data = json.loads(self._clean_json_response(question_response))
            except json.JSONDecodeError:
                logger.error("Failed to parse question-only response")
                return await self._generate_fallback_mcq(topic, difficulty)

            # Step 3: Generate distractors specifically
            distractor_prompt = self._create_distractor_prompt(
                combined_context,
                question_data['question'],
                question_data['correct_answer'],
                topic
            )
            distractor_response = await self.model_interface.generate_text(distractor_prompt)

            try:
                distractors = json.loads(self._clean_json_response(distractor_response))
            except json.JSONDecodeError:
                logger.error("Failed to parse distractor response")
                # Use basic distractors
                distractors = [
                    f"Alternative concept related to {topic}",
                    f"Common misconception about {topic}",
                    f"Related but incorrect principle"
                ]

            # Step 4: Combine into final MCQ
            final_mcq = {
                "question": question_data['question'],
                "options": {
                    "A": question_data['correct_answer'],
                    "B": distractors[0] if len(distractors) > 0 else "Alternative option",
                    "C": distractors[1] if len(distractors) > 1 else "Different approach",
                    "D": distractors[2] if len(distractors) > 2 else "Other method"
                },
                "correct": "A",
                "explanation": question_data.get('explanation', f"This is the correct answer based on the context about {topic}."),
                "generation_method": "two_step_distractors",
                "grounded": True,
                "cognitive_level": cognitive_level.value
            }

            # Validate the final result
            validation_result = self.validator.validate_mcq(final_mcq)
            if validation_result.is_valid:
                final_mcq['validation_score'] = validation_result.score
                logger.info("✅ Two-step MCQ generation successful")
                return final_mcq
            else:
                logger.warning("Two-step MCQ failed validation, applying fixes")
                return fix_common_mcq_issues(final_mcq)

        except Exception as e:
            logger.error(f"Error in two-step generation: {e}")
            return await self._generate_fallback_mcq(topic, difficulty)

    def _create_question_only_prompt(self, topic: str, context: str, difficulty: str, cognitive_level: CognitiveLevel) -> str:
        """Create prompt for generating question and correct answer only"""

        cognitive_instruction = self.cognitive_prompts.get(cognitive_level, self.cognitive_prompts[CognitiveLevel.UNDERSTANDING])
        target_audience = self._get_target_audience(difficulty)

        return f"""### ROLE & GOAL ###
You are a university professor creating an exam question. Generate ONLY a question and its correct answer based on the provided context about '{topic}'.

### COGNITIVE LEVEL ###
{cognitive_instruction}

### CONTEXT ###
{context}

### TASK ###
1. Identify a key concept from the context related to '{topic}'
2. Create a clear, specific question about this concept
3. Provide the correct answer based on the context
4. Write a brief explanation

### TARGET AUDIENCE ###
{target_audience}

### OUTPUT FORMAT ###
Respond with ONLY this JSON structure:
{{
  "question": "Your question here",
  "correct_answer": "The correct answer here",
  "explanation": "Brief explanation of why this is correct"
}}"""

    def _create_distractor_prompt(self, context: str, question: str, correct_answer: str, topic: str) -> str:
        """Create prompt for generating high-quality distractors"""

        return f"""### ROLE & GOAL ###
You are an expert test designer specializing in creating challenging distractors. Based on the context, question, and correct answer provided, generate three incorrect options that target common student misconceptions about '{topic}'.

### CONTEXT ###
{context}

### QUESTION ###
{question}

### CORRECT ANSWER ###
{correct_answer}

### TASK ###
Generate three plausible but incorrect options. They should:
1. Be similar in length and style to the correct answer
2. Target common misconceptions about the topic
3. Be factually incorrect according to the context
4. Sound plausible to someone who hasn't fully understood the concept

### OUTPUT FORMAT ###
Respond with ONLY a JSON array:
["Distractor A", "Distractor B", "Distractor C"]"""

    async def generate_multiple_grounded_mcqs(self, topic: str, num_questions: int = 5,
                                            difficulty: str = "medium",
                                            cognitive_levels: Optional[List[CognitiveLevel]] = None) -> List[Dict[str, Any]]:
        """Generate multiple grounded MCQs with varied cognitive levels"""

        if cognitive_levels is None:
            # Use a mix of cognitive levels
            cognitive_levels = [
                CognitiveLevel.UNDERSTANDING,
                CognitiveLevel.APPLYING,
                CognitiveLevel.ANALYZING,
                CognitiveLevel.REMEMBERING,
                CognitiveLevel.EVALUATING
            ]

        questions = []

        for i in range(num_questions):
            cognitive_level = cognitive_levels[i % len(cognitive_levels)]

            try:
                # Alternate between regular and two-step generation
                if i % 2 == 0:
                    mcq = await self.generate_grounded_mcq(
                        topic=f"{topic} (aspect {i+1})",
                        difficulty=difficulty,
                        cognitive_level=cognitive_level
                    )
                else:
                    mcq = await self.generate_with_two_step_distractors(
                        topic=f"{topic} (aspect {i+1})",
                        difficulty=difficulty,
                        cognitive_level=cognitive_level
                    )

                if mcq:
                    questions.append(mcq)
                    logger.info(f"Generated grounded MCQ {i+1}/{num_questions}")

            except Exception as e:
                logger.error(f"Failed to generate MCQ {i+1}: {e}")

        logger.info(f"✅ Generated {len(questions)} grounded MCQs for topic: {topic}")
        return questions

    async def generate_quiz_async(self, pure_content: str, topic: str, difficulty: str = "medium",
                                 cognitive_level: str = "understanding") -> Dict[str, Any]:
        """
        Generate MCQ using pure factual content (fixes prompt leakage!)

        Args:
            pure_content: Factual content about the topic (from RAG, not instructions)
            topic: The topic name for context
            difficulty: Difficulty level
            cognitive_level: Cognitive level for question complexity
        """
        try:
            logger.info(f"🎯 Generating MCQ from pure content (length: {len(pure_content)})")

            # Convert string cognitive level to enum
            cognitive_level_enum = self._string_to_cognitive_level(cognitive_level)

            # Create MCQ generation context with pure content
            context = MCQGenerationContext(
                topic=topic,
                context_passages=[pure_content],  # Pure content, not instructions!
                difficulty=difficulty,
                cognitive_level=cognitive_level_enum,
                generation_method="pure_content"
            )

            # Generate using the existing context-based method
            result = await self._generate_mcq_with_context(context)

            if result:
                # Mark as content-based generation
                result['generation_method'] = 'pure_content_based'
                result['grounded'] = True
                logger.info("✅ Successfully generated MCQ from pure content")
            else:
                logger.warning("⚠️ Pure content generation failed, using fallback")
                result = await self._generate_fallback_mcq(topic, difficulty)

            return result

        except Exception as e:
            logger.error(f"❌ Pure content MCQ generation failed: {e}")
            return await self._generate_fallback_mcq(topic, difficulty)

    def _string_to_cognitive_level(self, cognitive_level: str) -> CognitiveLevel:
        """Convert string cognitive level to enum"""
        mapping = {
            "understanding": CognitiveLevel.UNDERSTANDING,
            "applying": CognitiveLevel.APPLYING,
            "analyzing": CognitiveLevel.ANALYZING,
            "evaluating": CognitiveLevel.EVALUATING,
            "remembering": CognitiveLevel.REMEMBERING,
            "creating": CognitiveLevel.CREATING
        }
        return mapping.get(cognitive_level.lower(), CognitiveLevel.UNDERSTANDING)
