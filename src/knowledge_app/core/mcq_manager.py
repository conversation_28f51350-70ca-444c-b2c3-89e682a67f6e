"""
MCQ Manager - Unified interface for online and offline MCQ generation
Handles switching between Groq API and local models seamlessly
"""

import logging
from typing import Dict, List, Any, Optional
import asyncio

# CRITICAL MEMORY FIX: Defer torch import to reduce startup memory
# torch will be imported lazily when needed for cleanup
torch = None

logger = logging.getLogger(__name__)

class MCQManager:
    """Unified manager for MCQ generation with online/offline mode switching"""
    
    def __init__(self, config=None):
        self.config = config
        self.instant_generator = None
        self.offline_generator = None
        self.online_generator = None
        self.rag_generator = None
        self._offline_mode = False
        self._rag_mode = True  # Enable RAG by default when available
        self._instant_mode = True  # Enable instant generation by default

        # Initialize post-processor for enhanced quality
        self.post_processor = None
        self._use_post_processing = True

        # Initialize generators
        self._initialize_generators()
    
    def _initialize_generators(self):
        """Initialize instant, RAG, offline, and online generators"""
        try:
            # Initialize instant generator (highest priority - always works!)
            from .instant_mcq_generator import InstantMCQGenerator
            self.instant_generator = InstantMCQGenerator()
            logger.info("⚡ Instant MCQ generator ready - no model loading required!")

            # Initialize RAG-enhanced generator (high quality when available)
            from .rag_mcq_generator import RAGMCQGenerator
            self.rag_generator = RAGMCQGenerator()
            rag_ready = self.rag_generator.initialize()
            if rag_ready:
                logger.info("🔍 RAG MCQ generator ready with knowledge base")
            else:
                logger.warning("⚠️ RAG MCQ generator not ready - will use fallback")

            # Initialize offline generator (lazy loading - only when needed)
            from .offline_mcq_generator import OfflineMCQGenerator
            self.offline_generator = OfflineMCQGenerator()

            # Don't initialize offline generator during startup - only when explicitly requested
            logger.info("🏠 Offline MCQ generator ready for lazy initialization...")

            # Initialize online generator (existing inference system)
            from .inference import CloudInference
            if self.config:
                self.online_generator = CloudInference(self.config)

            # Initialize post-processor for enhanced quality
            if self._use_post_processing:
                try:
                    from .mcq_post_processor import MCQPostProcessor
                    self.post_processor = MCQPostProcessor()
                    logger.info("🔧 MCQ post-processor initialized for enhanced quality")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to initialize post-processor: {e}")
                    self._use_post_processing = False

            logger.info("✅ MCQ Manager initialized with instant, RAG, offline, and online capabilities")

        except Exception as e:
            logger.error(f"❌ Error initializing MCQ Manager: {e}")
    
    def set_offline_mode(self, offline: bool):
        """Set the MCQ generation mode"""
        self._offline_mode = offline
        logger.info(f"MCQ generation mode set to: {'Offline' if offline else 'Online'}")

    def set_rag_mode(self, rag_enabled: bool):
        """Enable or disable RAG-enhanced MCQ generation"""
        self._rag_mode = rag_enabled
        logger.info(f"RAG mode set to: {'Enabled' if rag_enabled else 'Disabled'}")

    def set_instant_mode(self, instant_enabled: bool):
        """Enable or disable instant MCQ generation"""
        self._instant_mode = instant_enabled
        logger.info(f"Instant mode set to: {'Enabled' if instant_enabled else 'Disabled'}")

    def is_offline_mode(self) -> bool:
        """Check if currently in offline mode"""
        return self._offline_mode

    def is_rag_mode(self) -> bool:
        """Check if RAG mode is enabled"""
        return self._rag_mode

    def is_instant_mode(self) -> bool:
        """Check if instant mode is enabled"""
        return self._instant_mode

    def is_rag_available(self) -> bool:
        """Check if RAG mode is available"""
        return self.rag_generator is not None and self.rag_generator.is_initialized

    def is_instant_available(self) -> bool:
        """Check if instant mode is available (always True!)"""
        if self.instant_generator is None:
            # Try to reinitialize instant generator if it's missing
            try:
                from .instant_mcq_generator import InstantMCQGenerator
                self.instant_generator = InstantMCQGenerator()
                logger.info("⚡ Instant MCQ generator reinitialized")
            except Exception as e:
                logger.error(f"❌ Failed to reinitialize instant generator: {e}")
                return False
        return self.instant_generator is not None
    
    def is_offline_available(self) -> bool:
        """Check if offline mode is available (without initializing)"""
        if not self.offline_generator:
            return False

        try:
            # Check if already initialized
            if self.offline_generator.is_initialized:
                return True

            # CRITICAL MEMORY FIX: Don't import torch during availability check
            # Just check if local model inference module is available
            try:
                from .local_model_inference import LocalModelInference
                # Don't check CUDA availability here to avoid importing torch during startup
                return True  # Module available, assume it will work when needed
            except ImportError as e:
                logger.warning(f"⚠️ Local model inference not available: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking offline availability: {e}")
            return False
    
    def is_online_available(self) -> bool:
        """Check if online mode is available"""
        if not self.online_generator:
            return False
        
        try:
            # Check if we have API configuration
            return hasattr(self.online_generator, 'client') and self.online_generator.client is not None
        except Exception:
            return False
    
    async def generate_quiz_async(self, context: str, difficulty: str = "medium",
                                 use_advanced: bool = True, cognitive_level: str = "understanding") -> Dict[str, Any]:
        """
        Generate a single MCQ question using the best available method.

        Args:
            context: The text content to generate questions from
            difficulty: Question difficulty level
            use_advanced: Whether to use advanced generation techniques
            cognitive_level: Bloom's taxonomy level (remembering, understanding, applying, analyzing, evaluating, creating)

        Returns:
            Dictionary containing question, options, correct answer, and explanation
        """
        try:
            logger.info(f"🎯 Generating MCQ: context='{context[:50]}...', difficulty='{difficulty}', advanced={use_advanced}")

            # Try advanced RAG generation first if enabled
            if use_advanced and self._rag_mode and self.is_rag_available():
                logger.info("🧠 Using advanced RAG MCQ generation with validation")
                try:
                    result = await self._generate_advanced_rag(context, difficulty, cognitive_level)
                    if result and result.get('question'):
                        # Apply post-processing for enhanced quality
                        return self._apply_post_processing(result, {
                            'topic': context,
                            'difficulty': difficulty,
                            'cognitive_level': cognitive_level
                        })
                    else:
                        logger.warning("⚠️ Advanced RAG generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Advanced RAG generation failed: {e}")

            # Try offline generation first if enabled and available (7B models)
            if self._offline_mode and self.is_offline_available():
                logger.info("🧠 Using offline 7B model MCQ generation")
                try:
                    result = await self._generate_offline(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Offline generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Offline generation failed: {e}")

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info("🔍 Using RAG-enhanced MCQ generation")
                try:
                    result = await self._generate_rag(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ RAG generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ RAG generation failed: {e}")

            # Try online generation if available
            if self.is_online_available():
                logger.info("🌐 Using online MCQ generation")
                try:
                    result = await self._generate_online(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Online generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Online generation failed: {e}")

            # Use instant generation as fallback only
            if self.is_instant_available():
                logger.info("⚡ Using instant MCQ generation (fallback - no model loading)")
                try:
                    result = await self._generate_instant(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Instant generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Instant generation failed: {e}")

            # If all else fails, use emergency fallback
            logger.warning("⚠️ All generation methods failed, using emergency fallback")
            return await self._generate_fallback(context, difficulty)

        except Exception as e:
            logger.error(f"❌ MCQ generation failed: {e}")
            return await self._generate_fallback(context, difficulty)
    
    async def _generate_offline(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using offline local models"""
        if not self.offline_generator:
            raise RuntimeError("Offline generator not available")

        # Smart initialization check - only initialize if truly needed
        if not self.offline_generator.is_initialized:
            logger.info("🔄 Initializing offline generator for MCQ generation...")
            if not self.offline_generator.initialize():
                raise RuntimeError("Failed to initialize offline generator")
        else:
            logger.debug("✅ Offline generator already initialized - reusing existing model")

        logger.info("🏠 Generating MCQ using local models (offline)")
        return await self.offline_generator.generate_quiz_async(context, difficulty)

    async def _generate_instant(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using instant content analysis (no model loading)"""
        if not self.instant_generator:
            raise RuntimeError("Instant generator not available")

        logger.info("⚡ Generating instant MCQ using content analysis")
        return await self.instant_generator.generate_quiz_async(context, difficulty)

    async def _generate_rag(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using RAG-enhanced approach"""
        if not self.rag_generator:
            raise RuntimeError("RAG generator not available")

        logger.info("🔍 Generating RAG-enhanced MCQ using knowledge base")
        return await self.rag_generator.generate_quiz_async(context, difficulty, use_rag=True)

    async def _generate_advanced_rag(self, context: str, difficulty: str, cognitive_level: str = "understanding") -> Dict[str, Any]:
        """Generate MCQ using advanced RAG with validation and quality control"""
        try:
            # Import the advanced generator
            from .advanced_rag_mcq_generator import AdvancedRAGMCQGenerator, CognitiveLevel

            # Map string to enum
            cognitive_map = {
                "remembering": CognitiveLevel.REMEMBERING,
                "understanding": CognitiveLevel.UNDERSTANDING,
                "applying": CognitiveLevel.APPLYING,
                "analyzing": CognitiveLevel.ANALYZING,
                "evaluating": CognitiveLevel.EVALUATING,
                "creating": CognitiveLevel.CREATING
            }

            cognitive_enum = cognitive_map.get(cognitive_level.lower(), CognitiveLevel.UNDERSTANDING)

            # Create advanced generator
            advanced_generator = AdvancedRAGMCQGenerator(
                rag_engine=getattr(self.rag_generator, 'rag_engine', None),
                model_interface=getattr(self.offline_generator, 'local_inference', None) if self.offline_generator else None
            )

            # Generate with advanced techniques
            result = await advanced_generator.generate_grounded_mcq(
                topic=context,
                difficulty=difficulty,
                cognitive_level=cognitive_enum
            )

            logger.info("✅ Advanced RAG MCQ generation completed")
            return result

        except Exception as e:
            logger.error(f"❌ Advanced RAG generation failed: {e}")
            # Fall back to regular RAG generation
            return await self._generate_rag(context, difficulty)

    async def _generate_online(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using online API (Groq)"""
        if not self.online_generator:
            raise RuntimeError("Online generator not available")
        
        logger.info("🌐 Generating MCQ using external API (online)")
        return await self.online_generator.generate_quiz_async(context, difficulty)
    
    async def _generate_fallback(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using fallback method when primary method fails"""
        try:
            # Try RAG mode if not already tried and available
            if not self._rag_mode and self.is_rag_available():
                logger.warning("⚠️ Primary generation failed, trying RAG fallback")
                return await self._generate_rag(context, difficulty)

            # Try the opposite mode as fallback
            if self._offline_mode and self.is_online_available():
                logger.warning("⚠️ Offline generation failed, trying online fallback")
                return await self._generate_online(context, difficulty)
            elif not self._offline_mode and self.is_offline_available():
                logger.warning("⚠️ Online generation failed, trying offline fallback")
                return await self._generate_offline(context, difficulty)
            else:
                # Generate a basic fallback question
                logger.warning("⚠️ All modes failed, using basic fallback")
                return self._generate_basic_fallback(context)

        except Exception as e:
            logger.error(f"❌ Fallback generation also failed: {e}")
            return self._generate_basic_fallback(context)
    
    def _generate_basic_fallback(self, context: str) -> Dict[str, Any]:
        """Generate a very basic fallback question using simple text analysis"""
        import re

        # Extract key words from context
        words = re.findall(r'\b[A-Za-z]{4,}\b', context.lower())
        common_words = {'that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'}
        key_words = [w for w in words if w not in common_words][:10]

        # Generate context-aware question
        if key_words:
            primary_topic = key_words[0].title()
            question = f"Based on the content about {primary_topic}, what is the main focus of the discussion?"

            # Generate options based on content
            options = {
                "A": f"Technical aspects of {primary_topic}",
                "B": f"General overview of {primary_topic}",
                "C": f"Historical development of {primary_topic}",
                "D": f"Practical applications of {primary_topic}"
            }

            explanation = f"This question focuses on the main theme related to {primary_topic} discussed in the content."
        else:
            # Ultra-basic fallback
            question = "Based on the provided content, what is the main topic being discussed?"
            options = {
                "A": "Technical specifications and details",
                "B": "General concepts and principles",
                "C": "Historical background and context",
                "D": "Practical applications and examples"
            }
            explanation = "This is a general question generated when AI model generation is unavailable."

        return {
            "question": question,
            "options": options,
            "correct": "B",  # Usually the general overview is a safe choice
            "explanation": explanation
        }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, 
                                  difficulty: str = "medium") -> List[Dict[str, Any]]:
        """
        Generate multiple MCQ questions using the current mode
        
        Args:
            context: The text content to generate questions from
            num_questions: Number of questions to generate
            difficulty: Question difficulty level
            
        Returns:
            List of question dictionaries
        """
        try:
            # Try instant generation first (no model loading!)
            if self._instant_mode and self.is_instant_available():
                logger.info(f"⚡ Generating {num_questions} instant MCQs (no model loading)")
                return self.instant_generator.generate_multiple_questions(context, num_questions, difficulty)

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info(f"🔍 Generating {num_questions} RAG-enhanced MCQs")
                return self.rag_generator.generate_multiple_questions(context, num_questions, difficulty)

            if self._offline_mode and self.offline_generator:
                logger.info(f"🏠 Generating {num_questions} MCQs using local models")
                return self.offline_generator.generate_multiple_questions(context, num_questions, difficulty)
            else:
                # For online mode, generate questions one by one
                logger.info(f"🌐 Generating {num_questions} MCQs using external API")
                return asyncio.run(self._generate_multiple_online(context, num_questions, difficulty))

        except Exception as e:
            logger.error(f"❌ Multiple question generation failed: {e}")
            return []

    def cleanup(self):
        """Clean up MCQ manager resources to prevent memory leaks and crashes"""
        try:
            logger.info("🧹 Starting MCQ manager cleanup...")

            # Clean up offline generator
            if self.offline_generator:
                try:
                    if hasattr(self.offline_generator, 'local_inference') and self.offline_generator.local_inference:
                        if hasattr(self.offline_generator.local_inference, 'unload_model'):
                            logger.info("🧹 Unloading offline model...")
                            self.offline_generator.local_inference.unload_model()
                    self.offline_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up offline generator: {e}")

            # Clean up online generator
            if self.online_generator:
                try:
                    # Close any open connections
                    if hasattr(self.online_generator, 'cleanup'):
                        self.online_generator.cleanup()
                    self.online_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up online generator: {e}")

            # Clean up RAG generator
            if self.rag_generator:
                try:
                    if hasattr(self.rag_generator, 'cleanup'):
                        self.rag_generator.cleanup()
                    self.rag_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up RAG generator: {e}")

            # Clean up instant generator
            if self.instant_generator:
                try:
                    if hasattr(self.instant_generator, 'cleanup'):
                        self.instant_generator.cleanup()
                    self.instant_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up instant generator: {e}")

            # Force garbage collection
            import gc
            gc.collect()
            gc.collect()  # Call twice for better cleanup

            # Clear CUDA cache if available (only if torch was imported)
            try:
                # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()  # Wait for all CUDA operations to complete
            except ImportError:
                # torch not available, skip CUDA cleanup
                pass

            logger.info("✅ MCQ manager cleanup completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during MCQ manager cleanup: {e}")
            # Still try basic cleanup
            try:
                import gc
                gc.collect()
                try:
                    # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
            except:
                pass
    
    async def _generate_multiple_online(self, context: str, num_questions: int, difficulty: str) -> List[Dict[str, Any]]:
        """Generate multiple questions using online API"""
        questions = []
        
        for i in range(num_questions):
            try:
                question = await self._generate_online(context, difficulty)
                if question:
                    questions.append(question)
                    logger.info(f"Generated online question {i+1}/{num_questions}")
            except Exception as e:
                logger.warning(f"Failed to generate online question {i+1}: {e}")
        
        return questions

    async def generate_quiz_async(self, quiz_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Orchestrates the entire high-quality MCQ generation process.
        1. Retrieves REAL content using RAG (not instructions!)
        2. Selects the best generator
        3. Generates the question using pure content
        4. Returns validated result

        This fixes the "prompt leakage" problem by ensuring content separation.
        """
        try:
            topic = quiz_params.get('topic', 'General Knowledge')
            difficulty = quiz_params.get('difficulty', 'medium')
            cognitive_level = quiz_params.get('cognitive_level', 'understanding')

            logger.info(f"🎯 Starting intelligent MCQ generation for '{topic}' (difficulty: {difficulty})")

            # --- STEP 1: The Grounded Scholar - Retrieve REAL Content ---
            logger.info(f"🔍 Using RAG Engine to retrieve factual content for topic: '{topic}'")

            real_content = await self._retrieve_pure_content(topic)
            if not real_content:
                logger.warning(f"⚠️ No content found for '{topic}'. Using curated fallback.")
                real_content = self._get_curated_fallback_content(topic)

            logger.info(f"📚 Retrieved content of length {len(real_content)} characters")

            # --- STEP 2: Select Best Generator ---
            generator = self._select_best_generator()
            if not generator:
                logger.error("❌ No suitable generator available")
                return self._generate_basic_fallback(topic)

            # --- STEP 3: Generate Question with Pure Content ---
            logger.info("🧠 Generating question using pure content (no instruction leakage)")

            if hasattr(generator, 'generate_quiz_async'):
                # Use async method if available
                result = await generator.generate_quiz_async(real_content, topic, difficulty, cognitive_level)
            else:
                # Fallback to sync method
                result = generator.generate_mcq(real_content, topic, difficulty, cognitive_level)

            # --- STEP 4: Apply Post-Processing ---
            if result and self._use_post_processing:
                result = self._apply_post_processing(result, quiz_params)

            logger.info("✅ High-quality MCQ generation completed successfully")
            return result

        except Exception as e:
            logger.error(f"❌ MCQ generation orchestration failed: {e}")
            return self._generate_basic_fallback(quiz_params.get('topic', 'Error'))

    async def _retrieve_pure_content(self, topic: str) -> str:
        """Retrieve pure factual content (not instructions) for the topic"""
        try:
            # Try advanced RAG first
            if self._rag_mode and self.is_rag_available():
                try:
                    from .advanced_rag_mcq_generator import AdvancedRAGMCQGenerator
                    if hasattr(self, 'advanced_rag_generator') and self.advanced_rag_generator:
                        # Use the RAG engine to get pure content
                        rag_engine = self.advanced_rag_generator.rag_engine
                        if rag_engine and hasattr(rag_engine, 'retrieve_context'):
                            context_chunks = rag_engine.retrieve_context(topic, top_k=3)
                            if context_chunks:
                                return "\n\n".join(context_chunks)
                except Exception as e:
                    logger.warning(f"⚠️ Advanced RAG content retrieval failed: {e}")

            # Try basic RAG fallback
            if hasattr(self, 'rag_generator') and self.rag_generator:
                try:
                    # Get context from basic RAG generator
                    context = self.rag_generator._get_context_for_topic(topic)
                    if context and len(context.strip()) > 50:
                        return context
                except Exception as e:
                    logger.warning(f"⚠️ Basic RAG content retrieval failed: {e}")

            return None

        except Exception as e:
            logger.error(f"❌ Content retrieval failed: {e}")
            return None

    def _get_curated_fallback_content(self, topic: str) -> str:
        """Get curated factual content when RAG fails"""
        # This provides real educational content, not instructions
        curated_content = {
            "magnetism": """
            Magnetism is a physical phenomenon produced by the motion of electric charge, resulting in attractive and repulsive forces between objects. Magnetic fields are created by moving electric charges and intrinsic magnetic moments of elementary particles. The strength of a magnetic field is measured in Tesla (T) or Gauss (G), where 1 Tesla = 10,000 Gauss.

            The Lorentz force describes the force on a charged particle moving through electric and magnetic fields: F = q(E + v × B), where q is the charge, E is the electric field, v is velocity, and B is the magnetic field. Ferromagnetic materials like iron, nickel, and cobalt can be permanently magnetized and are strongly attracted to magnets.

            Electromagnetic induction, discovered by Faraday, states that a changing magnetic field induces an electric field. This principle is fundamental to electric generators, transformers, and motors. Lenz's law states that induced currents flow in a direction to oppose the change that created them.
            """,
            "physics": """
            Physics is the fundamental science that seeks to understand how the universe works. It studies matter, energy, motion, forces, space, and time. Classical mechanics, developed by Newton, describes the motion of objects from projectiles to planets using concepts like force, momentum, and energy.

            Thermodynamics deals with heat, temperature, and energy transfer. The first law states that energy cannot be created or destroyed, only transformed. The second law introduces entropy and explains why heat flows from hot to cold objects.

            Quantum mechanics describes the behavior of matter and energy at atomic and subatomic scales. Wave-particle duality shows that particles can exhibit both wave and particle properties. The uncertainty principle states that position and momentum cannot be simultaneously measured with perfect precision.
            """,
            "programming": """
            Programming is the process of creating instructions for computers to execute. Modern programming languages like Python, Java, and C++ provide abstractions that make complex tasks manageable. Object-oriented programming organizes code into classes and objects, promoting code reuse and modularity.

            Algorithms are step-by-step procedures for solving problems. Time complexity measures how execution time grows with input size, typically expressed in Big O notation. Space complexity measures memory usage. Efficient algorithms are crucial for handling large datasets.

            Data structures organize and store data efficiently. Arrays provide constant-time access by index. Linked lists allow dynamic sizing. Hash tables provide average constant-time lookup. Trees enable hierarchical organization and efficient searching.
            """
        }

        # Get content for topic or provide general content
        topic_lower = topic.lower()
        for key, content in curated_content.items():
            if key in topic_lower or topic_lower in key:
                return content.strip()

        # Generic educational content
        return f"""
        {topic} is an important subject of study that encompasses various concepts, principles, and applications. Understanding {topic} requires examining its fundamental components, relationships between different elements, and practical implications.

        Key aspects of {topic} include theoretical foundations, practical applications, and connections to related fields. Students studying {topic} should focus on understanding core principles rather than memorizing isolated facts.

        Critical thinking and problem-solving skills are essential when working with {topic}. Real-world applications demonstrate the relevance and importance of mastering these concepts.
        """

    def _select_best_generator(self):
        """Select the best available generator for high-quality MCQ generation"""
        try:
            # Prefer advanced RAG generator for content-based generation
            if self._rag_mode and hasattr(self, 'advanced_rag_generator') and self.advanced_rag_generator:
                logger.info("🎯 Selected Advanced RAG Generator")
                return self.advanced_rag_generator

            # Fallback to offline generator
            if self._offline_mode and self.offline_generator:
                logger.info("🎯 Selected Offline Generator")
                return self.offline_generator

            # Last resort: basic RAG generator
            if hasattr(self, 'rag_generator') and self.rag_generator:
                logger.info("🎯 Selected Basic RAG Generator")
                return self.rag_generator

            logger.warning("⚠️ No suitable generator found")
            return None

        except Exception as e:
            logger.error(f"❌ Generator selection failed: {e}")
            return None

    def _generate_basic_fallback(self, topic: str) -> Dict[str, Any]:
        """Generate a basic fallback question when all else fails"""
        return {
            "question": f"What is an important concept related to {topic}?",
            "options": {
                "A": f"Basic principle of {topic}",
                "B": f"Advanced application of {topic}",
                "C": f"Historical development of {topic}",
                "D": f"Future trends in {topic}"
            },
            "correct": "A",
            "explanation": f"This is a basic question about {topic} concepts."
        }

    def _apply_post_processing(self, mcq_data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Apply post-processing to enhance MCQ quality"""
        try:
            if not self._use_post_processing or not self.post_processor:
                return mcq_data

            # Convert MCQ data to JSON string for processing
            import json
            mcq_json = json.dumps(mcq_data)

            # Process through pipeline
            result = self.post_processor.process_mcq(mcq_json, context)

            if result.success:
                logger.info(f"✅ Post-processing enhanced MCQ quality (score: {result.quality_score:.2f})")
                return result.mcq_data
            else:
                logger.warning(f"⚠️ Post-processing failed: {result.issues}")
                return mcq_data

        except Exception as e:
            logger.error(f"❌ Post-processing error: {e}")
            return mcq_data

    def set_post_processing(self, enabled: bool):
        """Enable or disable post-processing"""
        self._use_post_processing = enabled
        logger.info(f"Post-processing set to: {'Enabled' if enabled else 'Disabled'}")

    def is_post_processing_enabled(self) -> bool:
        """Check if post-processing is enabled"""
        return self._use_post_processing and self.post_processor is not None
    
    def get_mode_status(self) -> Dict[str, Any]:
        """Get current mode status and availability"""
        return {
            "current_mode": "offline" if self._offline_mode else "online",
            "instant_enabled": self._instant_mode,
            "instant_available": self.is_instant_available(),
            "rag_enabled": self._rag_mode,
            "rag_available": self.is_rag_available(),
            "offline_available": self.is_offline_available(),
            "online_available": self.is_online_available(),
            "can_switch": True
        }
    
    def get_mode_info(self) -> str:
        """Get human-readable mode information"""
        instant_status = "⚡ Instant" if self.is_instant_available() and self._instant_mode else ""
        rag_status = "🔍 RAG Enhanced" if self.is_rag_available() and self._rag_mode else ""

        # Instant mode takes priority
        if self._instant_mode and self.is_instant_available():
            return f"⚡ Instant Mode - No model loading required {rag_status}".strip()

        if self._offline_mode:
            if self.is_offline_available():
                return f"🏠 Offline Mode - Using local models {rag_status}".strip()
            else:
                return "⚠️ Offline Mode - Local models not available"
        else:
            if self.is_online_available():
                return f"🌐 Online Mode - Using external API {rag_status}".strip()
            else:
                return "⚠️ Online Mode - API not configured"
    
    def switch_mode(self):
        """Switch between online and offline modes"""
        new_mode = not self._offline_mode
        
        # Check if the new mode is available
        if new_mode and not self.is_offline_available():
            logger.warning("Cannot switch to offline mode - local models not available")
            return False
        elif not new_mode and not self.is_online_available():
            logger.warning("Cannot switch to online mode - API not configured")
            return False
        
        self.set_offline_mode(new_mode)
        return True
    
    def update_config(self, config):
        """Update configuration and reinitialize if needed"""
        self.config = config
        
        # Check if offline mode setting changed
        if config and hasattr(config, 'get_value'):
            offline_mode = config.get_value('mcq_settings.offline_mode', False)
            if offline_mode != self._offline_mode:
                self.set_offline_mode(offline_mode)
        
        # Reinitialize online generator with new config
        try:
            from .inference import CloudInference
            self.online_generator = CloudInference(config)
        except Exception as e:
            logger.warning(f"Failed to update online generator config: {e}")

# Global instance for easy access
_mcq_manager = None

def get_mcq_manager(config=None) -> MCQManager:
    """Get the global MCQ manager instance"""
    global _mcq_manager
    if _mcq_manager is None:
        _mcq_manager = MCQManager(config)
    elif config:
        _mcq_manager.update_config(config)
    return _mcq_manager
