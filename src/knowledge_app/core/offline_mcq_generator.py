"""
Offline MCQ Generator using Local Models
Provides offline capability for MCQ generation while maintaining feature parity with online implementation
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    import torch
except ImportError:
    torch = None

logger = logging.getLogger(__name__)

class OfflineMCQGenerator:
    """Generate MCQs using locally fine-tuned models"""
    
    def __init__(self):
        self.local_inference = None
        self.is_initialized = False
        self._model_locked = False  # Prevent model unloading during generation
        self._generation_in_progress = False
        # Add async lock for model access
        import asyncio
        self.model_lock = asyncio.Lock()
        # Don't auto-initialize during construction to prevent automatic model loading
        
    def initialize(self):
        """Initialize the local model for MCQ generation with enhanced state management and device consistency"""
        try:
            # Check if we already have a local_inference instance (from external assignment)
            if hasattr(self, 'local_inference') and self.local_inference is not None:
                logger.info("🔄 Using existing LocalModelInference instance")
                # Verify the existing instance is properly loaded and device-consistent
                if self.local_inference.is_loaded and self.local_inference._is_model_ready():
                    # Additional device consistency check
                    try:
                        model_device = self.local_inference._get_model_device()
                        expected_device = self.local_inference.device
                        if model_device != expected_device:
                            logger.warning(f"⚠️ Device mismatch in existing instance: model on {model_device}, expected {expected_device}")
                            # Try to fix device mismatch
                            if hasattr(self.local_inference.model, 'to'):
                                logger.info(f"🔧 Correcting device mismatch: moving model to {expected_device}")
                                self.local_inference.model = self.local_inference.model.to(expected_device)
                                logger.info("✅ Device mismatch corrected")

                        self.is_initialized = True
                        logger.info("✅ Offline MCQ generator using pre-loaded model with device consistency verified")
                        return True
                    except Exception as device_error:
                        logger.warning(f"⚠️ Device consistency check failed: {device_error}")
                        # Continue with model loading attempt
                else:
                    logger.warning("⚠️ Existing instance not ready, attempting to load model")
                    if self.local_inference.load_model():
                        self.is_initialized = True
                        logger.info("✅ Offline MCQ generator initialized with existing instance")
                        return True

            # Create new instance if none exists
            from .local_model_inference import LocalModelInference, LocalModelConfig
            from .real_7b_config import SUPPORTED_MODELS

            # Create config with instruct models from real_7b_config
            instruct_models = [
                model_config.model_id for model_config in SUPPORTED_MODELS.values()
                if "instruct" in model_config.name.lower() or "it" in model_config.name.lower()
            ]

            config = LocalModelConfig(
                base_model_name="mistralai/Mistral-7B-Instruct-v0.2",  # Start with 7B instruct model
                fallback_models=instruct_models,  # Use only instruct models for quality
                load_in_4bit=True,  # Use 4-bit quantization for 7B models
                max_new_tokens=600,  # Longer for detailed MCQ generation
                temperature=0.7,
                top_p=0.9
            )

            logger.info("🔄 Creating new LocalModelInference instance")
            self.local_inference = LocalModelInference(config)

            # Load the model
            logger.info("🔄 Loading model for offline MCQ generation")
            if self.local_inference.load_model():
                self.is_initialized = True
                logger.info("✅ Offline MCQ generator initialized with new instance")
                return True
            else:
                logger.warning("⚠️ No local models available for offline MCQ generation")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to initialize offline MCQ generator: {e}")
            return False

    def _find_best_trained_model(self) -> Optional[str]:
        """Find the best available trained model (LoRA adapters or fine-tuned models)"""
        import os
        from pathlib import Path

        # Check for LoRA adapters
        lora_paths = [
            "data/lora_adapters_mistral",
            "data/lora_adapters",
            "lora_adapters_mistral",
            "lora_adapters"
        ]

        for lora_path in lora_paths:
            if os.path.exists(lora_path):
                # Look for subdirectories with adapter_config.json
                for subdir in os.listdir(lora_path):
                    adapter_path = os.path.join(lora_path, subdir)
                    if os.path.isdir(adapter_path):
                        adapter_config = os.path.join(adapter_path, "adapter_config.json")
                        if os.path.exists(adapter_config):
                            logger.info(f"Found LoRA adapter: {adapter_path}")
                            return adapter_path

        # Check for fine-tuned models
        model_paths = [
            "data/fine_tuned_models",
            "fine_tuned_models",
            "data/models"
        ]

        for model_path in model_paths:
            if os.path.exists(model_path):
                # Look for subdirectories with config.json
                for subdir in os.listdir(model_path):
                    full_model_path = os.path.join(model_path, subdir)
                    if os.path.isdir(full_model_path):
                        config_file = os.path.join(full_model_path, "config.json")
                        if os.path.exists(config_file):
                            logger.info(f"Found fine-tuned model: {full_model_path}")
                            return full_model_path

        logger.info("No trained models found")
        return None
    
    async def generate_quiz_async(self, context: str, difficulty: str = "medium") -> Dict[str, Any]:
        """
        Generate a single MCQ question asynchronously using local models with enhanced state validation

        Args:
            context: The text content to generate questions from
            difficulty: Question difficulty level

        Returns:
            Dictionary containing question, options, correct answer, and explanation
        """
        # Smart initialization: only initialize if not already initialized and ready
        if not self.is_initialized or not hasattr(self, 'local_inference') or self.local_inference is None:
            logger.info("🔄 Initializing offline MCQ generator (first time or after failure)")
            if not self.initialize():
                logger.error("❌ Failed to initialize offline MCQ generator")
                logger.error("❌ This usually means:")
                logger.error("   • No CUDA-compatible GPU available")
                logger.error("   • No local models installed")
                logger.error("   • Insufficient GPU memory")
                logger.error("   • Model loading timeout")
                return self._generate_fallback_question(context)
        else:
            logger.info("✅ Using existing initialized offline MCQ generator")

        # Validate model state before generation (less aggressive approach)
        try:
            # Basic validation
            if not hasattr(self, 'local_inference') or self.local_inference is None:
                logger.warning("⚠️ local_inference is None, attempting to reinitialize...")
                if not self.initialize():
                    logger.error("❌ Failed to reinitialize, using fallback")
                    return self._generate_fallback_question(context)

            # Check if model is loaded
            if not self.local_inference.is_loaded:
                logger.info("🔄 Model not loaded, loading now...")
                if not self.local_inference.load_model():
                    logger.error("❌ Failed to load model, using fallback")
                    return self._generate_fallback_question(context)

            # Verify model readiness
            if not self.local_inference._is_model_ready():
                logger.warning("⚠️ Model not ready, attempting one reload...")
                if not self.local_inference.load_model():
                    logger.warning("⚠️ Model reload failed, but continuing with generation attempt")
                    # Don't fail completely - sometimes the model works even if _is_model_ready() returns False

            logger.info("✅ Model validation completed - proceeding with generation")

        except Exception as e:
            logger.error(f"❌ Error during model validation: {e}")
            logger.info("🔄 Attempting to continue with generation despite validation error")
            # Don't fail completely - try to generate anyway

        try:
            # Prepare for generation with proper resource management
            logger.info("🧹 Preparing for MCQ generation with proper resource management")
            self._cleanup_after_generation()

            # Lock model to prevent unloading during generation
            self._generation_in_progress = True
            self._model_locked = True
            self.local_inference.lock_model()  # Lock at inference level too

            logger.info(f"🧠 Generating offline MCQ (difficulty: {difficulty})")
            logger.info(f"🔍 Model state: loaded={self.local_inference.is_loaded}, ready={self.local_inference._is_model_ready()}")
            logger.info(f"🔒 Model locked for generation: {self._model_locked}")

            # Perform device consistency check before generation
            if not self._check_device_consistency():
                logger.warning("⚠️ Device consistency issue detected, attempting to fix...")
                if not self._fix_device_consistency():
                    # Don't fail completely for device issues, try to continue
                    logger.warning("⚠️ Device consistency issues detected but continuing with generation")

            # Create MCQ generation prompt
            prompt = self._create_mcq_prompt(context, difficulty)

            # Generate response using local model with comprehensive error handling
            try:
                logger.info("🔄 Starting text generation...")

                # Double-check model state before generation
                if not self.local_inference.is_loaded:
                    raise RuntimeError("Model not loaded. Call load_model() first.")

                response = self.local_inference.generate_text(
                    prompt,
                    max_new_tokens=600,
                    temperature=0.7,
                    top_p=0.9
                )
                logger.info("✅ Text generation completed successfully")

                # Force garbage collection after generation to prevent memory buildup
                import gc
                gc.collect()
                if torch and torch.cuda.is_available():
                    torch.cuda.empty_cache()

            except Exception as gen_error:
                logger.error(f"❌ Model generation failed: {gen_error}")
                # Check if it's a device-related error
                if "expected all tensors to be on the same device" in str(gen_error).lower():
                    logger.error("❌ Device mismatch error detected during generation")
                    logger.error("❌ This indicates model and input tensors are on different devices")
                    # Try to fix device consistency for next attempt
                    try:
                        self._fix_device_consistency()
                    except Exception as fix_error:
                        logger.error(f"❌ Failed to fix device consistency: {fix_error}")

                # Clean up memory even on error
                import gc
                gc.collect()
                if torch and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                return self._generate_fallback_question(context)
            
            # Log the raw response for debugging
            logger.info(f"🔍 Raw model response: {response[:500]}...")

            # Parse response into structured format
            result = self._parse_mcq_response(response)

            if result:
                logger.info("✅ Offline MCQ generation successful")
                return result
            else:
                logger.warning("⚠️ Failed to parse MCQ response")
                logger.warning(f"⚠️ Raw response was: {response}")
                return self._generate_fallback_question(context)

        except Exception as e:
            logger.error(f"❌ Offline MCQ generation failed: {e}")
            return self._generate_fallback_question(context)

        finally:
            # Always unlock model after generation
            self._generation_in_progress = False
            self._model_locked = False
            if hasattr(self, 'local_inference') and self.local_inference:
                self.local_inference.unlock_model()  # Unlock at inference level too
            logger.debug("🔓 Model unlocked after generation")
    
    def _create_mcq_prompt(self, context: str, difficulty: str) -> str:
        """Create an advanced Inquisitor's Mandate prompt for high-quality MCQ generation"""

        difficulty_map = {
            "easy": "a high-school student",
            "medium": "an undergraduate university student",
            "hard": "a graduate student specializing in the field",
            "expert": "a post-doctoral researcher"
        }
        difficulty_level = difficulty_map.get(difficulty.lower(), "an undergraduate university student")

        prompt = f"""### ROLE & GOAL ###
You are a meticulous and demanding university professor designing a final exam. Your goal is to create ONE challenging multiple-choice question that tests a deep understanding of a key concept from the provided context. Your question must not be a simple definition recall. It must require reasoning.

### CONTEXT ###
{context.strip()}

### TASK & CONSTRAINTS ###
1. **Identify a Core Concept:** From the CONTEXT, identify a single, non-obvious concept, relationship, or implication.
2. **Formulate the Question:** Create a clear, specific question about this core concept. The question must be answerable *only* from the provided CONTEXT.
3. **Create the Correct Answer:** Write a correct answer that is a thoughtful paraphrase of information in the context, not a direct quote.
4. **Create Plausible Distractors:** Generate three incorrect but highly plausible "distractor" options. These distractors should target common misconceptions related to the topic. They must be factually incorrect *according to the context*.
5. **Write the Explanation:** Provide a concise explanation for why the correct answer is correct and briefly explain why the other options are incorrect, citing the context.

### TARGET AUDIENCE ###
This question is designed for {difficulty_level}.

### STRICT OUTPUT FORMAT ###
You MUST respond with ONLY a single, raw JSON object. Do not include any introductory text, markdown formatting like ```json, or any other text outside of the JSON structure.

{{
  "question": "Your question text here.",
  "options": {{
    "A": "Plausible distractor A.",
    "B": "The correct answer, paraphrased from the context.",
    "C": "Plausible distractor C.",
    "D": "Plausible distractor D."
  }},
  "correct": "B",
  "explanation": "Your detailed explanation here."
}}"""

        return prompt
    
    def _parse_mcq_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse the model's response into structured MCQ format"""
        try:
            # Clean up the response
            response = response.strip()
            
            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                try:
                    result = json.loads(json_str)
                    
                    # Validate required fields
                    required_fields = ['question', 'options', 'correct', 'explanation']
                    if all(field in result for field in required_fields):
                        
                        # Validate options format
                        if isinstance(result['options'], dict) and len(result['options']) >= 4:
                            return result
                        
                except json.JSONDecodeError:
                    pass
            
            # If JSON parsing fails, try to parse manually
            return self._parse_manual_format(response)
            
        except Exception as e:
            logger.error(f"Error parsing MCQ response: {e}")
            return None
    
    def _parse_manual_format(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse MCQ from non-JSON format"""
        try:
            lines = response.split('\n')
            result = {}
            options = {}
            
            # Extract question
            for line in lines:
                if line.strip() and not line.startswith(('A)', 'B)', 'C)', 'D)', 'A.', 'B.', 'C.', 'D.')):
                    if 'question' not in result and '?' in line:
                        result['question'] = line.strip()
                        break
            
            # Extract options
            option_pattern = re.compile(r'^([ABCD])[\.\)]\s*(.+)$')
            for line in lines:
                match = option_pattern.match(line.strip())
                if match:
                    letter, text = match.groups()
                    options[letter] = text.strip()
            
            if len(options) >= 4:
                result['options'] = options
                
                # Try to find correct answer
                correct_patterns = [
                    r'correct.*?([ABCD])',
                    r'answer.*?([ABCD])',
                    r'([ABCD]).*?correct'
                ]
                
                for pattern in correct_patterns:
                    match = re.search(pattern, response, re.IGNORECASE)
                    if match:
                        result['correct'] = match.group(1)
                        break
                
                if 'correct' not in result:
                    result['correct'] = 'A'  # Default fallback
                
                # Extract explanation
                explanation_patterns = [
                    r'explanation[:\-\s]*(.+?)(?:\n|$)',
                    r'because[:\-\s]*(.+?)(?:\n|$)',
                    r'reason[:\-\s]*(.+?)(?:\n|$)'
                ]
                
                for pattern in explanation_patterns:
                    match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
                    if match:
                        result['explanation'] = match.group(1).strip()
                        break
                
                if 'explanation' not in result:
                    result['explanation'] = f"The correct answer is {result.get('correct', 'A')}."
                
                # Validate we have all required fields
                if all(field in result for field in ['question', 'options', 'correct', 'explanation']):
                    return result
            
            return None
            
        except Exception as e:
            logger.error(f"Error in manual parsing: {e}")
            return None
    
    def _generate_fallback_question(self, context: str) -> Dict[str, Any]:
        """Generate a simple fallback question when parsing fails"""
        
        # Extract a key concept from the context
        words = context.split()
        key_concept = "the topic" if len(words) < 5 else " ".join(words[:5])
        
        return {
            "question": f"Based on the provided content, what is the main focus regarding {key_concept}?",
            "options": {
                "A": "The content provides detailed technical information",
                "B": "The content offers a general overview", 
                "C": "The content focuses on practical applications",
                "D": "The content presents theoretical concepts"
            },
            "correct": "B",
            "explanation": "This is a fallback question generated when the local model response could not be parsed properly."
        }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, 
                                  difficulty: str = "medium") -> List[Dict[str, Any]]:
        """
        Generate multiple MCQ questions from content
        
        Args:
            context: The text content to generate questions from
            num_questions: Number of questions to generate
            difficulty: Question difficulty level
            
        Returns:
            List of question dictionaries
        """
        if not self.is_initialized:
            if not self.initialize():
                return []
        
        questions = []
        
        try:
            # Split content into chunks for varied questions
            content_chunks = self._split_content(context, num_questions)
            
            for i, chunk in enumerate(content_chunks):
                try:
                    # Use synchronous version for batch generation
                    result = self._generate_single_question_sync(chunk, difficulty)
                    if result:
                        questions.append(result)
                        logger.info(f"Generated question {i+1}/{num_questions}")
                except Exception as e:
                    logger.warning(f"Failed to generate question {i+1}: {e}")
                    
            logger.info(f"✅ Generated {len(questions)} offline MCQ questions")
            return questions
            
        except Exception as e:
            logger.error(f"❌ Error generating multiple questions: {e}")
            return []
    
    def _split_content(self, content: str, num_parts: int) -> List[str]:
        """Split content into chunks for generating varied questions"""
        words = content.split()
        if len(words) <= num_parts:
            return [content] * num_parts
        
        chunk_size = len(words) // num_parts
        chunks = []
        
        for i in range(num_parts):
            start = i * chunk_size
            end = start + chunk_size + 20  # Add overlap for context
            if i == num_parts - 1:  # Last chunk gets remaining words
                end = len(words)
            
            chunk = " ".join(words[start:end])
            chunks.append(chunk)
        
        return chunks
    
    def _generate_single_question_sync(self, context: str, difficulty: str) -> Optional[Dict[str, Any]]:
        """Synchronous version of single question generation"""
        try:
            prompt = self._create_mcq_prompt(context, difficulty)
            response = self.local_inference.generate_text(
                prompt,
                max_new_tokens=600,
                temperature=0.8,  # Slightly higher for variety
                top_p=0.9
            )
            return self._parse_mcq_response(response)
        except Exception as e:
            logger.error(f"Error in sync question generation: {e}")
            return None

    def _check_device_consistency(self) -> bool:
        """Check if model and expected device are consistent"""
        try:
            if not self.local_inference or not self.local_inference.model:
                return True  # No model loaded, no consistency issue

            model_device = self.local_inference._get_model_device()
            expected_device = self.local_inference.device

            # Handle cuda:0 vs cuda device naming
            def normalize_device(device_str):
                if device_str == "cuda:0":
                    return "cuda"
                return device_str

            normalized_model_device = normalize_device(model_device)
            normalized_expected_device = normalize_device(expected_device)

            if normalized_model_device == normalized_expected_device:
                logger.debug(f"✅ Device consistency check passed: {model_device} (normalized: {normalized_model_device})")
                return True
            else:
                logger.warning(f"⚠️ Device mismatch: model on {model_device}, expected {expected_device}")
                return False

        except Exception as e:
            logger.warning(f"⚠️ Device consistency check failed: {e}")
            return False

    def _fix_device_consistency(self) -> bool:
        """Attempt to fix device consistency issues"""
        try:
            if not self.local_inference or not self.local_inference.model:
                return True  # No model to fix

            model_device = self.local_inference._get_model_device()
            expected_device = self.local_inference.device

            # Handle cuda:0 vs cuda device naming
            def normalize_device(device_str):
                if device_str == "cuda:0":
                    return "cuda"
                return device_str

            normalized_model_device = normalize_device(model_device)
            normalized_expected_device = normalize_device(expected_device)

            if normalized_model_device == normalized_expected_device:
                logger.debug(f"✅ Device consistency acceptable: {model_device} vs {expected_device}")
                return True  # Already consistent (accounting for cuda:0 vs cuda)

            logger.info(f"🔧 Attempting to move model from {model_device} to {expected_device}")

            # Try to move model to correct device
            if hasattr(self.local_inference.model, 'to'):
                self.local_inference.model = self.local_inference.model.to(expected_device)

                # Verify the move was successful
                new_device = self.local_inference._get_model_device()
                new_normalized = normalize_device(new_device)
                if new_normalized == normalized_expected_device:
                    logger.info(f"✅ Successfully moved model to {expected_device}")
                    return True
                else:
                    # For cuda:0 vs cuda, this is acceptable
                    if "cuda" in new_device and "cuda" in expected_device:
                        logger.info(f"💡 Device difference acceptable: {new_device} vs {expected_device}")
                        return True
                    else:
                        logger.error(f"❌ Model move failed: still on {new_device}")
                        return False
            else:
                logger.error("❌ Model does not support device movement")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to fix device consistency: {e}")
            return False

    def _cleanup_after_generation(self):
        """Perform proper cleanup after MCQ generation"""
        try:
            logger.info("🧹 Performing cleanup after MCQ generation")

            # Allow normal garbage collection
            import gc
            collected = gc.collect()
            logger.info(f"🧹 Garbage collection freed {collected} objects")

            # Clear GPU cache if available
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                allocated = torch.cuda.memory_allocated() / 1024**2  # MB
                logger.info(f"🎮 GPU memory after cleanup: {allocated:.1f}MB")

            logger.info("✅ Generator cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during generator cleanup: {e}", exc_info=True)

    async def generate_quiz_async(self, pure_content: str, topic: str, difficulty: str = "medium",
                                 cognitive_level: str = "understanding") -> Dict[str, Any]:
        """
        Generate MCQ using pure factual content (fixes prompt leakage!)

        Args:
            pure_content: Factual content about the topic (from RAG, not instructions)
            topic: The topic name for context
            difficulty: Difficulty level
            cognitive_level: Cognitive level for question complexity
        """
        try:
            logger.info(f"🎯 Offline generator using pure content (length: {len(pure_content)})")

            # Ensure model is initialized
            if not self.is_initialized:
                logger.info("🔧 Initializing offline model for pure content generation")
                self.initialize()
                if not self.is_initialized:
                    logger.error("❌ Failed to initialize offline model")
                    return None

            # Lock the model for generation
            async with self.model_lock:
                try:
                    # Create the Inquisitor's Mandate prompt with pure content
                    prompt = self._create_inquisitor_prompt_pure_content(pure_content, topic, difficulty, cognitive_level)

                    logger.info("🧠 Generating MCQ with Inquisitor's Mandate (pure content)")

                    # Generate response
                    response = self.local_inference.generate_text(
                        prompt,
                        max_new_tokens=600,
                        temperature=0.7,
                        do_sample=True
                    )

                    if not response or len(response.strip()) < 10:
                        logger.warning("⚠️ Empty or too short response from model")
                        return None

                    # Parse the JSON response
                    mcq_data = self._parse_mcq_response(response)

                    if mcq_data:
                        # Mark as pure content generation
                        mcq_data['generation_method'] = 'offline_pure_content'
                        mcq_data['grounded'] = True
                        logger.info("✅ Successfully generated MCQ from pure content")
                        return mcq_data
                    else:
                        logger.warning("⚠️ Failed to parse MCQ response")
                        return None

                except Exception as e:
                    logger.error(f"❌ Error during offline generation: {e}")
                    return None

        except Exception as e:
            logger.error(f"❌ Offline pure content generation failed: {e}")
            return None

    def _create_inquisitor_prompt_pure_content(self, pure_content: str, topic: str, difficulty: str, cognitive_level: str) -> str:
        """
        Create the Inquisitor's Mandate prompt using pure factual content
        This fixes the prompt leakage problem by ensuring the CONTEXT section
        contains only factual information, not instructions.
        """

        # Map difficulty to target audience
        difficulty_map = {
            "easy": "a high-school student",
            "medium": "an undergraduate university student",
            "hard": "a graduate student specializing in the field",
            "expert": "a post-doctoral researcher"
        }
        difficulty_level = difficulty_map.get(difficulty.lower(), "an undergraduate university student")

        # Map cognitive level to specific instructions
        cognitive_instructions = {
            "understanding": "The question should test comprehension and explanation of concepts from the context.",
            "applying": "The question should require using knowledge from the context in new situations or problem-solving.",
            "analyzing": "The question should require breaking down information and examining relationships within the context.",
            "evaluating": "The question should require making judgments, critiques, or assessments based on the context.",
            "remembering": "The question should test recall of important facts and concepts from the context.",
            "creating": "The question should require producing new ideas or solutions based on the context."
        }
        cognitive_instruction = cognitive_instructions.get(cognitive_level.lower(), cognitive_instructions["understanding"])

        prompt = f"""### ROLE & GOAL ###
You are a meticulous and demanding university professor designing a final exam. Your goal is to develop ONE challenging multiple-choice question that tests a deep understanding of a key concept from the provided context about '{topic}'. Your question must not be a simple definition recall. It must require reasoning.

Target Audience: {difficulty_level}
Cognitive Level: {cognitive_level.title()} - {cognitive_instruction}

### CONTEXT ###
{pure_content}

### TASK & CONSTRAINTS ###
1. **Identify a Core Concept:** From the CONTEXT above, identify a single, non-obvious concept, relationship, or implication.
2. **Formulate the Question:** Develop a clear, specific question about this core concept. The question must be answerable *only* from the provided CONTEXT.
3. **Develop the Correct Answer:** Write a correct answer that is a thoughtful paraphrase of information in the context, not a direct quote.
4. **Develop Plausible Distractors:** Generate three incorrect but highly plausible "distractor" options. These distractors should target common misconceptions related to the topic. They must be factually incorrect *according to the context*.
5. **Write the Explanation:** Provide a concise explanation for why the correct answer is correct and briefly explain why the other options are incorrect, citing the context.

### STRICT OUTPUT FORMAT ###
You MUST respond with ONLY a single, raw JSON object. Do not include any introductory text, markdown formatting like ```json, or any other text outside of the JSON structure.

{{
  "question": "Your question text here.",
  "options": {{
    "A": "Plausible distractor A.",
    "B": "The correct answer, paraphrased from the context.",
    "C": "Plausible distractor C.",
    "D": "Plausible distractor D."
  }},
  "correct": "B",
  "explanation": "Your detailed explanation here, referencing the context."
}}"""

        return prompt
